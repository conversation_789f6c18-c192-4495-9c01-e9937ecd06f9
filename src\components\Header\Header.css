.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 2rem;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 400px;
}

.header-contents-left {
  flex: 1;
  max-width: 50%;
}

.header-contents-left h2 {
  font-size: 2.5rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem;
  line-height: 1.2;
}

.header-contents-left p {
  font-size: 1.1rem;
  color: #666;
  line-height: 1.6;
  margin-bottom: 2rem;
}

.header-contents-left button {
  background-color: #517276;
  color: white;
  border: none;
  padding: 12px 30px;
  font-size: 1rem;
  border-radius: 25px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.header-contents-left button:hover {
  background-color: #3e5a5e;
}

.header-contents-right {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.header-contents-right img {
  max-width: 100%;
  height: auto;
  border-radius: 10px;
}
