.explore-section {
  display: flex;
  padding: 2rem 4rem; /* Reduced padding for smaller screens */
  flex-direction: column;
  align-items: center;
  gap: 2rem;
  animation: fadeIn 2s;
  text-align: center; /* Center-align the text */
}

.explore-images {
  display: grid;
  /* Default to 4 equal-width columns for large screens */
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  width: 100%;
}

.image-container {
  /* Style the container instead of the image directly */
  width: 100%;
  padding-top: 100%; /* This creates a square aspect ratio */
  position: relative;
  border-radius: 4px;
  overflow: hidden;
}

.explore-images img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover; /* Crops image to fit without distortion */
  object-position: center; /* Centers the crop */
}

@media (max-width: 1024px) {
  .explore-images {
    /* Switch to 2 equal-width columns */
    grid-template-columns: repeat(2, 1fr);
  }
}
/* Add a media query for even better responsiveness on small screens */
@media (max-width: 768px) {
  .explore-section {
    padding: 2rem 2rem; /* Further reduce padding */
  }

  .explore-images {
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  }
}
