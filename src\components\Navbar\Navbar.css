.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 20px;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.navbar .logo {
  height: 80px; /* Adjust this value to make logo smaller/larger */
  width: auto; /* Maintains aspect ratio */
  object-fit: contain; /* Ensures image fits nicely */
}

.navbar-menu {
  display: flex;
  list-style: none;
  gap: 15px;
  margin: 0;
  padding: 0;
  align-items: center;
  font-size: 18px;
}

.navbar-menu li {
  cursor: pointer;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.navbar-menu li:hover,
.navbar-menu li.active {
  background-color: #f0f0f0;
}

.navbar-right {
  position: relative; /* This is crucial for the dot positioning */
  display: flex;
  align-items: center;
}

.navbar-right .cart {
  height: 30px;
  width: auto;
  cursor: pointer;
}

.navbar .active {
  border-bottom: 2px solid #517276;
  padding-bottom: 2px;
}

.navbar-right .dot {
  position: absolute;
  min-width: 10px;
  min-height: 10px;
  background-color: tomato;
  border-radius: 5px;
  top: -8px;
  right: -8px;
}

.navbar-right {
  position: relative;
}
